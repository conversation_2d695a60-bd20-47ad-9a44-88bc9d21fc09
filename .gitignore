# ===================================
# 项目级别 Git 忽略规则
# 适用于 Java Spring Boot + Vue + UniApp 混合项目
# ===================================

# ===================================
# 日志文件
# ===================================
# 忽略所有 .log 扩展名的日志文件（包括子目录）
*.log
logs/
# 忽略日志目录中的所有文件
**/logs/
# 常见日志文件模式
*.log.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Java 项目忽略规则
# ===================================
# Maven 构建产物
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# 编译生成的 .class 文件
*.class

# JAR 文件（除了 wrapper jar）
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# IDE 配置文件
.idea/
*.iws
*.iml
*.ipr
.vscode/
.settings/
.project
.classpath
.factorypath
.springBeans
.sts4-cache

# Eclipse
.apt_generated
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

# ===================================
# 前端项目忽略规则
# ===================================
# 依赖目录
node_modules/
jspm_packages/

# 构建输出目录
dist/
dist-ssr/
build/
out/

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 前端缓存和临时文件
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/
.dynamodb/
.tern-port

# 测试覆盖率
coverage/
*.lcov
.nyc_output

# 前端测试相关
/cypress/videos/
/cypress/screenshots/

# TypeScript 构建信息
*.tsbuildinfo

# ===================================
# UniApp 特定规则
# ===================================
# UniApp 构建输出
unpackage/
# UniApp 临时文件
.hbuilderx/

# ===================================
# 上传文件目录
# ===================================
# 忽略上传文件但保留目录结构
uploads/*
!uploads/.gitkeep

# ===================================
# 操作系统生成的文件
# ===================================
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# 编辑器和工具
# ===================================
# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# ===================================
# 临时文件和缓存
# ===================================
# 通用临时文件
*.tmp
*.temp
*.cache
*.pid
*.seed
*.pid.lock

# 文本编辑器临时文件
*.txt~
*.bak
*.orig

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.tar
*.zip

# ===================================
# 数据库文件
# ===================================
*.db
*.sqlite
*.sqlite3

# ===================================
# 其他项目特定文件
# ===================================
# 临时测试文件
t*.txt
test*.txt
temp*.txt

# 备份文件
*.backup
*.bak

# 配置文件备份
*.conf.bak
*.properties.bak
*.yml.bak
*.yaml.bak
*.json.bak
