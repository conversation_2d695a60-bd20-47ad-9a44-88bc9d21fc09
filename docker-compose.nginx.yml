# Docker Compose配置 - 使用Nginx代理的完整部署方案
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: expert-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 1234
      MYSQL_DATABASE: expert_db
      MYSQL_USER: expert
      MYSQL_PASSWORD: expert123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./expert/src/main/resources/sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - expert-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: expert-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - expert-network

  # Spring Boot后端
  backend:
    build:
      context: ./expert
      dockerfile: Dockerfile
    container_name: expert-backend
    restart: unless-stopped
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=expert_db
      - DB_USERNAME=root
      - DB_PASSWORD=1234
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "9090:9090"
    depends_on:
      - mysql
      - redis
    volumes:
      - backend_uploads:/app/uploads
    networks:
      - expert-network

  # Vue前端
  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile
    container_name: expert-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost/api
    networks:
      - expert-network

  # Nginx代理服务器
  nginx:
    image: nginx:alpine
    container_name: expert-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro # SSL证书目录（如果需要HTTPS）
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - expert-network

volumes:
  mysql_data:
  redis_data:
  backend_uploads:
  nginx_logs:


networks:
  expert-network:
    driver: bridge
