<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qing.expert.mapper.CategoryMapper">

    <!-- 获取启用状态的分类列表 -->
    <select id="selectEnabledCategories" resultType="com.qing.expert.entity.Category">
        SELECT id, name, description, icon, icon_type, icon_color, sort_order, status, create_time, update_time
        FROM categories
        WHERE deleted = 0 AND status = 1
        ORDER BY sort_order ASC, id ASC
    </select>

    <!-- 根据名称查询分类 -->
    <select id="selectByName" resultType="com.qing.expert.entity.Category">
        SELECT id, name, description, icon, icon_type, icon_color, sort_order, status, create_time, update_time
        FROM categories
        WHERE name = #{name} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 获取最大排序值 -->
    <select id="getMaxSortOrder" resultType="java.lang.Integer">
        SELECT MAX(sort_order)
        FROM categories
        WHERE deleted = 0
    </select>

    <!-- 检查分类是否被使用 -->
    <select id="checkCategoryInUse" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM (
            SELECT 1 FROM experts WHERE category_id = #{categoryId} AND deleted = 0
            UNION ALL
            SELECT 1 FROM services WHERE category_id = #{categoryId} AND deleted = 0
        ) t
    </select>

</mapper>
