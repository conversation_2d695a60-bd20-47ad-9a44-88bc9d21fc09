@import "./base.css";

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background-color: var(--color-background-page);
  overflow-x: hidden;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  width: 100vw;
  background-color: var(--color-background-page);
  position: relative;
}

/* 防止页面闪烁 */
.router-view {
  min-height: 100vh;
  background-color: var(--color-background-page);
}

/* 现代化滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-soft);
  border-radius: var(--radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Element Plus 现代化样式覆盖 */
.el-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--color-border-light);
  transition: all var(--transition-base);
}

.el-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-border);
}

.el-card__header {
  background-color: var(--color-background-soft);
  border-bottom: 1px solid var(--color-border-light);
  padding: var(--spacing-lg) var(--spacing-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.el-card__body {
  padding: var(--spacing-xl);
}

.el-button {
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.el-button--primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-500) 100%);
  border-color: var(--primary-600);
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-600) 100%);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.el-input__wrapper {
  border-radius: var(--radius-md);
  border-color: var(--color-border);
  transition: all var(--transition-fast);
}

.el-input__wrapper:hover {
  border-color: var(--color-border-hover);
}

.el-input__wrapper.is-focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.el-select .el-input__wrapper {
  border-radius: var(--radius-md);
}

.el-table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-base);
}

.el-table th.el-table__cell {
  background-color: var(--color-background-soft);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
  border-bottom: 1px solid var(--color-border);
}

.el-table td.el-table__cell {
  border-bottom: 1px solid var(--color-border-light);
}

.el-table__row:hover > td {
  background-color: var(--color-background-soft);
}

.el-pagination {
  justify-content: center;
}

.el-pagination .el-pager li {
  border-radius: var(--radius-base);
  margin: 0 2px;
  transition: all var(--transition-fast);
}

.el-pagination .el-pager li:hover {
  background-color: var(--primary-100);
  color: var(--color-primary);
}

.el-pagination .el-pager li.is-active {
  background-color: var(--color-primary);
  color: white;
}

.el-tag {
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
}

.el-dialog {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.el-dialog__header {
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--color-border-light);
}

.el-dialog__title {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.el-form-item__label {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 现代化工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-muted {
  color: var(--color-text-muted);
}

.text-success {
  color: var(--success-600);
}

.text-warning {
  color: var(--warning-600);
}

.text-error {
  color: var(--error-600);
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-base); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-base); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-base); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-base); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-base); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-base); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-base); }
.pt-4 { padding-top: var(--spacing-lg); }
.pt-5 { padding-top: var(--spacing-xl); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-base); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pb-5 { padding-bottom: var(--spacing-xl); }

/* 布局工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 圆角工具类 */
.rounded-sm {
  border-radius: var(--radius-sm);
}

.rounded {
  border-radius: var(--radius-base);
}

.rounded-md {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.rounded-full {
  border-radius: 9999px;
}

/* 阴影工具类 */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow {
  box-shadow: var(--shadow-base);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* 过渡动画 */
.transition {
  transition: all var(--transition-base);
}

.transition-fast {
  transition: all var(--transition-fast);
}

.transition-slow {
  transition: all var(--transition-slow);
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* 现代化卡片样式 */
.modern-card {
  background: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--color-border-light);
  transition: all var(--transition-base);
}

.modern-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-border);
}

/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-500) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-600) 0%, var(--secondary-500) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
}

.gradient-error {
  background: linear-gradient(135deg, var(--error-600) 0%, var(--error-500) 100%);
}
