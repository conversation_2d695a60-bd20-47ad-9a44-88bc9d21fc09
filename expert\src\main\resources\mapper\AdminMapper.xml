<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qing.expert.mapper.AdminMapper">

    <!-- 根据用户名查询管理员 -->
    <select id="selectByUsername" resultType="com.qing.expert.entity.Admin">
        SELECT id, username, password, real_name, phone, email, avatar, role, status, 
               last_login_time, last_login_ip, create_time, update_time, deleted
        FROM admins
        WHERE username = #{username} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 更新最后登录信息 -->
    <update id="updateLastLogin">
        UPDATE admins
        SET last_login_time = NOW(),
            last_login_ip = #{loginIp},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
