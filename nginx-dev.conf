# Nginx开发环境配置 - 简化版
# 适用于本地开发环境，快速解决403问题

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 基本配置
    sendfile on;
    keepalive_timeout 65;
    client_max_body_size 100M;
    
    # 开发环境服务器配置
    server {
        listen 3000;  # 使用3000端口避免冲突
        server_name localhost;
        
        # API接口代理 - 核心配置
        # 将 /api/* 请求代理到后端的 /*
        location /api/ {
            # 重写URL，去掉/api前缀
            rewrite ^/api/(.*)$ /$1 break;
            
            # 代理到Spring Boot后端
            proxy_pass http://localhost:9090;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS配置
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }
        
        # 静态资源代理
        location /static/ {
            proxy_pass http://localhost:9090/static/;
            proxy_set_header Host $host;
        }

        # 文件代理
        location /files/ {
            proxy_pass http://localhost:9090/files/;
            proxy_set_header Host $host;
        }

        # 头像代理
        location /avatars/ {
            proxy_pass http://localhost:9090/avatars/;
            proxy_set_header Host $host;
        }
        
        # 管理后台前端代理（如果需要）
        location / {
            proxy_pass http://localhost:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持（如果前端有热重载）
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
